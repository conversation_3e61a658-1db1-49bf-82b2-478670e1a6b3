{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue?vue&type=style&index=0&id=134e98f7&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue", "mtime": 1754370985843}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog5qGI5Lu25L+h5oGv5Y2h54mHICovDQouY2FzZS1pbmZvLWNhcmQgLmNhcmQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouY2FzZS1pbmZvLWNhcmQgLmNhcmQtdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLmNhc2UtaW5mby1jYXJkIC5pbmZvLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5jYXNlLWluZm8tY2FyZCAuaW5mby1pdGVtIC5sYWJlbCB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQogIG1pbi13aWR0aDogODBweDsNCn0NCg0KLmNhc2UtaW5mby1jYXJkIC5pbmZvLWl0ZW0gLnZhbHVlIHsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmNhc2UtaW5mby1jYXJkIC5pbmZvLWl0ZW0gLnZhbHVlLmFtb3VudCB7DQogIGNvbG9yOiAjRTZBMjNDOw0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLyog5YKs6K6w54q25oCB5qC35byPICovDQouc3RhdHVzLXRyYWNraW5nIHsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5zdGF0dXMtYXBwb2ludG1lbnQgew0KICBjb2xvcjogIzY3QzIzQTsNCn0NCg0KLnN0YXR1cy11bmFibGUgew0KICBjb2xvcjogI0Y1NkM2QzsNCn0NCg0KLyog562b6YCJ5Yy65Z+fICovDQouZmlsdGVyLXNlY3Rpb24gew0KICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KICBwYWRkaW5nOiAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQp9DQoNCi8qIOinhuWbvuWIh+aNoiAqLw0KLnZpZXctdG9nZ2xlIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQovKiDml7bpl7Tnur/lrrnlmaggKi8NCi50aW1lbGluZS1jb250YWluZXIgew0KICBtYXgtaGVpZ2h0OiA1MDBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgcGFkZGluZzogMCAyMHB4Ow0KfQ0KDQovKiDml7bpl7Tnur/ljaHniYcgKi8NCi50aW1lbGluZS1jYXJkIHsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCn0NCg0KLnRpbWVsaW5lLWNhcmQgLnRpbWVsaW5lLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTJweDsNCn0NCg0KLnRpbWVsaW5lLWNhcmQgLnRpbWVsaW5lLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi50aW1lbGluZS1jYXJkIC50aW1lbGluZS1vcGVyYXRvciB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi50aW1lbGluZS1jYXJkIC50aW1lbGluZS1jb250ZW50IHAgew0KICBtYXJnaW46IDhweCAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQoudGltZWxpbmUtY2FyZCAudGltZWxpbmUtY29udGVudCBzdHJvbmcgew0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLnRpbWVsaW5lLWNhcmQgLnRpbWVsaW5lLWFjdGlvbnMgew0KICBtYXJnaW4tdG9wOiAxMnB4Ow0KICB0ZXh0LWFsaWduOiByaWdodDsNCn0NCg0KLyog56m654q25oCBICovDQouZW1wdHktc3RhdGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDQwcHggMDsNCn0NCg0KLyog6Ieq5a6a5LmJ5q2l6aqk5p2h5qC35byPIC0g5r+A5rS76IqC54K55Li66JOd6ImyICovDQo6OnYtZGVlcCAuY3VzdG9tLXN0ZXBzIC5lbC1zdGVwX19oZWFkLmlzLXByb2Nlc3Mgew0KICBjb2xvcjogIzQwOWVmZjsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KfQ0KDQo6OnYtZGVlcCAuY3VzdG9tLXN0ZXBzIC5lbC1zdGVwX19oZWFkLmlzLXByb2Nlc3MgLmVsLXN0ZXBfX2ljb24gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDA5ZWZmOw0KICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQo6OnYtZGVlcCAuY3VzdG9tLXN0ZXBzIC5lbC1zdGVwX190aXRsZS5pcy1wcm9jZXNzIHsNCiAgY29sb3I6ICM0MDllZmY7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQovKiDml7bpl7Tnur/moLflvI/kvJjljJYgKi8NCjo6di1kZWVwIC5lbC10aW1lbGluZS1pdGVtX190aW1lc3RhbXAgew0KICBmb250LXNpemU6IDEzcHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQo6OnYtZGVlcCAuZWwtdGltZWxpbmUtaXRlbV9fd3JhcHBlciB7DQogIHBhZGRpbmctbGVmdDogMjhweDsNCn0NCg0KOjp2LWRlZXAgLmVsLXRpbWVsaW5lLWl0ZW1fX3RhaWwgew0KICBib3JkZXItbGVmdDogMnB4IHNvbGlkICNlNGU3ZWQ7DQp9DQoNCi8qIOihqOagvOagt+W8j+S8mOWMliAqLw0KOjp2LWRlZXAgLmVsLXRhYmxlIC5lbC10YWJsZV9faGVhZGVyLXdyYXBwZXIgew0KICBiYWNrZ3JvdW5kOiAjZmFmYWZhOw0KfQ0KDQo6OnYtZGVlcCAuZWwtdGFibGUgLmVsLXRhYmxlX19yb3c6aG92ZXIgPiB0ZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQp9DQoNCi8qIOWvueivneahhuW6lemDqOaMiemSriAqLw0KLmRpYWxvZy1mb290ZXIgew0KICB0ZXh0LWFsaWduOiByaWdodDsNCn0NCg0KLmRpYWxvZy1mb290ZXIgLmVsLWJ1dHRvbiB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KfQ0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICA6OnYtZGVlcCAubGl0aWdhdGlvbi1sb2ctZGlhbG9nIHsNCiAgICB3aWR0aDogOTUlICFpbXBvcnRhbnQ7DQogIH0NCg0KICAuY2FzZS1pbmZvLWNhcmQgLmVsLWNvbCB7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgfQ0KDQogIC5maWx0ZXItc2VjdGlvbiAuZWwtY29sIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["litigationLogView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgjBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "litigationLogView.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"法诉日志查看\" width=\"1200px\" @close=\"handleCancel\" class=\"litigation-log-dialog\">\r\n      <!-- 案件基本信息 -->\r\n      <div class=\"case-info-card\" v-if=\"data\">\r\n        <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">案件基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">贷款人：</span>\r\n                <span class=\"value\">{{ data.贷款人 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">身份证：</span>\r\n                <span class=\"value\">{{ data.身份证 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">车牌号：</span>\r\n                <span class=\"value\">{{ data.车辆牌号 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">法诉文员：</span>\r\n                <span class=\"value\">{{ data.法诉文员 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 10px;\">\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">发起法诉日：</span>\r\n                <span class=\"value\">{{ formatDate(data.发起法诉日) || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">法诉状态：</span>\r\n                <span class=\"value\">{{ data.法诉子状态 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">剩余金额：</span>\r\n                <span class=\"value amount\">{{ formatMoney(data.剩余金额) || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">最新催记类型：</span>\r\n                <span class=\"value\" :class=\"getUrgeStatusClass(data.日志类型)\">\r\n                  {{ getUrgeStatusText(data.日志类型) || '-' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </div>\r\n\r\n      <!-- 进度条 -->\r\n      <el-steps :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 筛选和搜索 -->\r\n      <div class=\"filter-section\" style=\"margin-bottom: 20px;\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-input\r\n              v-model=\"searchKeyword\"\r\n              placeholder=\"搜索日志内容、操作人\"\r\n              prefix-icon=\"el-icon-search\"\r\n              clearable\r\n              @input=\"handleSearch\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-select v-model=\"filterStatus\" placeholder=\"筛选状态\" clearable @change=\"handleFilter\">\r\n              <el-option label=\"全部\" value=\"\" />\r\n              <el-option v-for=\"status in allStatuses\" :key=\"status\" :label=\"status\" :value=\"status\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              @change=\"handleDateFilter\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"refreshData\">刷新</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 视图切换 -->\r\n      <div class=\"view-toggle\" style=\"margin-bottom: 20px;\">\r\n        <el-radio-group v-model=\"viewMode\" @change=\"handleViewModeChange\">\r\n          <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          <el-radio-button label=\"timeline\">时间线视图</el-radio-button>\r\n        </el-radio-group>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\" v-loading=\"loading\">\r\n        <el-table :data=\"filteredLogList\" border style=\"width: 100%; margin-bottom: 24px\" :height=\"400\">\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n          <el-table-column prop=\"createTime\" label=\"操作时间\" width=\"160\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.createTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"操作人\" width=\"120\" align=\"center\" />\r\n          <el-table-column prop=\"status\" label=\"法诉状态\" width=\"140\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\r\n                {{ scope.row.status || '-' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"docName\" label=\"文档名称\" width=\"150\" align=\"center\" />\r\n          <el-table-column prop=\"docNumber\" label=\"文档编号\" width=\"120\" align=\"center\" />\r\n          <el-table-column prop=\"openDate\" label=\"生效日期\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDate(scope.row.openDate) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"100\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"mini\"\r\n                @click=\"viewLogDetail(scope.row)\"\r\n                v-if=\"scope.row.docUploadUrl\"\r\n              >\r\n                查看文档\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 时间线视图 -->\r\n      <div v-if=\"viewMode === 'timeline'\" v-loading=\"loading\" class=\"timeline-container\">\r\n        <el-timeline>\r\n          <el-timeline-item\r\n            v-for=\"(log, index) in filteredLogList\"\r\n            :key=\"index\"\r\n            :timestamp=\"formatDateTime(log.createTime)\"\r\n            placement=\"top\"\r\n            :type=\"getTimelineType(log.status)\"\r\n            :color=\"getTimelineColor(log.status)\"\r\n            size=\"large\"\r\n          >\r\n            <el-card shadow=\"hover\" class=\"timeline-card\">\r\n              <div class=\"timeline-header\">\r\n                <span class=\"timeline-title\">{{ log.status || '状态更新' }}</span>\r\n                <span class=\"timeline-operator\">{{ log.createBy }}</span>\r\n              </div>\r\n              <div class=\"timeline-content\">\r\n                <p v-if=\"log.docName\"><strong>文档：</strong>{{ log.docName }}</p>\r\n                <p v-if=\"log.docNumber\"><strong>编号：</strong>{{ log.docNumber }}</p>\r\n                <p v-if=\"log.openDate\"><strong>生效日期：</strong>{{ formatDate(log.openDate) }}</p>\r\n                <div v-if=\"log.docUploadUrl\" class=\"timeline-actions\">\r\n                  <el-button type=\"text\" size=\"mini\" @click=\"viewLogDetail(log)\">查看文档</el-button>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"filteredLogList.length === 0\" class=\"empty-state\">\r\n          <el-empty description=\"暂无日志记录\" />\r\n        </div>\r\n      </div>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\" icon=\"el-icon-document\">催记日志</el-button>\r\n        <el-button @click=\"exportLogs\" icon=\"el-icon-download\">导出日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        if (newVal && newVal.序号) {\r\n          this.loadLogData()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      loading: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '撤案已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '执行终本', value: '执行终本' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n        { label: '撤案', value: '撤案' },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n      // 新增的数据属性\r\n      searchKeyword: '',\r\n      filterStatus: '',\r\n      dateRange: [],\r\n      viewMode: 'table', // table 或 timeline\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤\r\n    statusSteps() {\r\n      return this.litigationStatusTree.map(item => item.label)\r\n    },\r\n\r\n    // 筛选后的日志列表\r\n    filteredLogList() {\r\n      let filtered = [...this.logList]\r\n\r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase()\r\n        filtered = filtered.filter(log =>\r\n          (log.docName && log.docName.toLowerCase().includes(keyword)) ||\r\n          (log.createBy && log.createBy.toLowerCase().includes(keyword)) ||\r\n          (log.status && log.status.toLowerCase().includes(keyword)) ||\r\n          (log.docNumber && log.docNumber.toLowerCase().includes(keyword))\r\n        )\r\n      }\r\n\r\n      // 状态筛选\r\n      if (this.filterStatus) {\r\n        filtered = filtered.filter(log => log.status === this.filterStatus)\r\n      }\r\n\r\n      // 日期范围筛选\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        const startDate = new Date(this.dateRange[0])\r\n        const endDate = new Date(this.dateRange[1])\r\n        endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间\r\n\r\n        filtered = filtered.filter(log => {\r\n          if (!log.createTime) return false\r\n          const logDate = new Date(log.createTime)\r\n          return logDate >= startDate && logDate <= endDate\r\n        })\r\n      }\r\n\r\n      // 按时间倒序排列\r\n      return filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))\r\n    },\r\n\r\n    // 所有状态列表（用于筛选下拉框）\r\n    allStatuses() {\r\n      const statuses = new Set()\r\n      this.logList.forEach(log => {\r\n        if (log.status) {\r\n          statuses.add(log.status)\r\n        }\r\n      })\r\n      return Array.from(statuses).sort()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载日志数据\r\n    async loadLogData() {\r\n      if (!this.data || !this.data.序号) return\r\n\r\n      this.loading = true\r\n      try {\r\n        const res = await listLitigation_log({ litigationId: this.data.序号 })\r\n        this.logList = res.rows || []\r\n\r\n        // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n        if (this.logList.length > 0) {\r\n          const lastLogStatus = this.logList[this.logList.length - 1].status\r\n          this.setActiveStepByStatus(lastLogStatus)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载日志数据失败:', error)\r\n        this.$message.error('加载日志数据失败')\r\n        this.logList = []\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch() {\r\n      // 搜索逻辑在computed中处理，这里可以添加防抖等优化\r\n    },\r\n\r\n    // 状态筛选处理\r\n    handleFilter() {\r\n      // 筛选逻辑在computed中处理\r\n    },\r\n\r\n    // 日期筛选处理\r\n    handleDateFilter() {\r\n      // 日期筛选逻辑在computed中处理\r\n    },\r\n\r\n    // 刷新数据\r\n    refreshData() {\r\n      this.loadLogData()\r\n    },\r\n\r\n    // 视图模式切换\r\n    handleViewModeChange() {\r\n      // 视图切换逻辑\r\n    },\r\n\r\n    // 查看日志详情\r\n    viewLogDetail(log) {\r\n      if (log.docUploadUrl) {\r\n        // 打开文档链接\r\n        window.open(log.docUploadUrl, '_blank')\r\n      } else {\r\n        this.$message.info('暂无文档链接')\r\n      }\r\n    },\r\n\r\n    // 导出日志\r\n    exportLogs() {\r\n      if (this.filteredLogList.length === 0) {\r\n        this.$message.warning('暂无数据可导出')\r\n        return\r\n      }\r\n\r\n      // 这里可以实现导出功能\r\n      this.$message.success('导出功能开发中...')\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '-'\r\n      return new Date(date).toLocaleDateString('zh-CN')\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(datetime) {\r\n      if (!datetime) return '-'\r\n      return new Date(datetime).toLocaleString('zh-CN')\r\n    },\r\n\r\n    // 格式化金额\r\n    formatMoney(amount) {\r\n      if (amount === null || amount === undefined || amount === '') {\r\n        return '0.00'\r\n      }\r\n      return Number(amount).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n    },\r\n\r\n    // 获取催记类型文本\r\n    getUrgeStatusText(value) {\r\n      const urgeStatusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进'\r\n      }\r\n      return urgeStatusMap[value] || value || '-'\r\n    },\r\n\r\n    // 获取催记类型样式类\r\n    getUrgeStatusClass(value) {\r\n      const classMap = {\r\n        1: 'status-tracking',\r\n        2: 'status-appointment',\r\n        3: 'status-unable'\r\n      }\r\n      return classMap[value] || ''\r\n    },\r\n\r\n    // 获取状态标签类型\r\n    getStatusTagType(status) {\r\n      const typeMap = {\r\n        '准备资料': 'info',\r\n        '待立案': 'warning',\r\n        '待出民初号': 'warning',\r\n        '待开庭': 'primary',\r\n        '待出法院文书': 'primary',\r\n        '执行中': 'success',\r\n        '结案': 'success',\r\n        '撤案': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n\r\n    // 获取时间线类型\r\n    getTimelineType(status) {\r\n      const typeMap = {\r\n        '结案': 'success',\r\n        '撤案': 'danger',\r\n        '执行中': 'primary'\r\n      }\r\n      return typeMap[status] || 'primary'\r\n    },\r\n\r\n    // 获取时间线颜色\r\n    getTimelineColor(status) {\r\n      const colorMap = {\r\n        '准备资料': '#909399',\r\n        '待立案': '#E6A23C',\r\n        '待出民初号': '#E6A23C',\r\n        '待开庭': '#409EFF',\r\n        '待出法院文书': '#409EFF',\r\n        '执行中': '#67C23A',\r\n        '结案': '#67C23A',\r\n        '撤案': '#F56C6C'\r\n      }\r\n      return colorMap[status] || '#409EFF'\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n      this.loadLogData()\r\n    },\r\n\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n\r\n    handleConfirm() {\r\n      this.visible = false\r\n    },\r\n\r\n    handleCancel() {\r\n      this.visible = false\r\n      // 重置筛选条件\r\n      this.searchKeyword = ''\r\n      this.filterStatus = ''\r\n      this.dateRange = []\r\n      this.viewMode = 'table'\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 案件信息卡片 */\r\n.case-info-card .card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.case-info-card .card-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.case-info-card .info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.case-info-card .info-item .label {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  min-width: 80px;\r\n}\r\n\r\n.case-info-card .info-item .value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.case-info-card .info-item .value.amount {\r\n  color: #E6A23C;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 催记状态样式 */\r\n.status-tracking {\r\n  color: #409EFF;\r\n}\r\n\r\n.status-appointment {\r\n  color: #67C23A;\r\n}\r\n\r\n.status-unable {\r\n  color: #F56C6C;\r\n}\r\n\r\n/* 筛选区域 */\r\n.filter-section {\r\n  background: #f5f7fa;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 视图切换 */\r\n.view-toggle {\r\n  text-align: center;\r\n}\r\n\r\n/* 时间线容器 */\r\n.timeline-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n/* 时间线卡片 */\r\n.timeline-card {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.timeline-card .timeline-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.timeline-card .timeline-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.timeline-card .timeline-operator {\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.timeline-card .timeline-content p {\r\n  margin: 8px 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.timeline-card .timeline-content strong {\r\n  color: #303133;\r\n}\r\n\r\n.timeline-card .timeline-actions {\r\n  margin-top: 12px;\r\n  text-align: right;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 40px 0;\r\n}\r\n\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 时间线样式优化 */\r\n::v-deep .el-timeline-item__timestamp {\r\n  font-size: 13px;\r\n  color: #909399;\r\n}\r\n\r\n::v-deep .el-timeline-item__wrapper {\r\n  padding-left: 28px;\r\n}\r\n\r\n::v-deep .el-timeline-item__tail {\r\n  border-left: 2px solid #e4e7ed;\r\n}\r\n\r\n/* 表格样式优化 */\r\n::v-deep .el-table .el-table__header-wrapper {\r\n  background: #fafafa;\r\n}\r\n\r\n::v-deep .el-table .el-table__row:hover > td {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 对话框底部按钮 */\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  ::v-deep .litigation-log-dialog {\r\n    width: 95% !important;\r\n  }\r\n\r\n  .case-info-card .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .filter-section .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n"]}]}