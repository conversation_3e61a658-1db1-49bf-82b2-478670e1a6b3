{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue", "mtime": 1754370985843}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TGl0aWdhdGlvbl9sb2cgfSBmcm9tICdAL2FwaS9saXRpZ2F0aW9uL2xpdGlnYXRpb24nDQppbXBvcnQgTG9hblJlbWluZGVyTG9nIGZyb20gJ0AvbGF5b3V0L2NvbXBvbmVudHMvRGlhbG9nL2xvYW5SZW1pbmRlckxvZy52dWUnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0xpdGlnYXRpb25Mb2dWaWV3JywNCiAgY29tcG9uZW50czogew0KICAgIExvYW5SZW1pbmRlckxvZywNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBkYXRhOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pLA0KICAgIH0sDQogIH0sDQogIHdhdGNoOiB7DQogICAgZGF0YTogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ25ld1ZhbDonLCBuZXdWYWwpDQogICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsLuW6j+WPtykgew0KICAgICAgICAgIHRoaXMubG9hZExvZ0RhdGEoKQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiB0cnVlDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdmlzaWJsZTogZmFsc2UsDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOivieiuvOeKtuaAgeagkee7k+aehA0KICAgICAgcmVtaW5kZXJMb2dMb2FuSWQ6ICcnLA0KICAgICAgbGl0aWdhdGlvblN0YXR1c1RyZWU6IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn56uL5qGI5YmNJywNCiAgICAgICAgICB2YWx1ZTogJ+eri+ahiOWJjScsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICflh4blpIfotYTmlpknLCB2YWx1ZTogJ+WHhuWkh+i1hOaWmScgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICflt7Lpgq7lr4QnLCB2YWx1ZTogJ+aSpOahiOW3sumCruWvhCcgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICflvoXnq4vmoYgnLCB2YWx1ZTogJ+W+heeri+ahiCcgfSwNCiAgICAgICAgICBdLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfnq4vmoYgt5Yik5YazJywNCiAgICAgICAgICB2YWx1ZTogJ+eri+ahiC3liKTlhrMnLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5Ye65rCR5Yid5Y+3JywgdmFsdWU6ICflvoXlh7rmsJHliJ3lj7cnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5byA5bqtJywgdmFsdWU6ICflvoXlvIDluq0nIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5Ye65rOV6Zmi5paH5LmmJywgdmFsdWU6ICflvoXlh7rms5XpmaLmlofkuaYnIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5Yik5YazLeaJp+ihjCcsDQogICAgICAgICAgdmFsdWU6ICfliKTlhrMt5omn6KGMJywNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAgeyBsYWJlbDogJ+W+heWHuueUs+ivt+S5picsIHZhbHVlOiAn5b6F5Ye655Sz6K+35LmmJyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+W3suaPkOS6pOaJp+ihjOS5picsIHZhbHVlOiAn5bey5o+Q5Lqk5omn6KGM5LmmJyB9LA0KICAgICAgICAgIF0sDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+aJp+ihjOWQjicsDQogICAgICAgICAgdmFsdWU6ICfmiafooYzlkI4nLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5omn6KGM5LitJywgdmFsdWU6ICfmiafooYzkuK0nIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F6YCB6L2mJywgdmFsdWU6ICflvoXpgIHovaYnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5rOV5ouNJywgdmFsdWU6ICflvoXms5Xmi40nIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn57un57ut5omn6KGMJywgdmFsdWU6ICfnu6fnu63miafooYwnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5omn6KGM57uI5pysJywgdmFsdWU6ICfmiafooYznu4jmnKwnIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn57uT5qGIJywNCiAgICAgICAgICB2YWx1ZTogJ+e7k+ahiCcsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICfms5Xor4nlh4/lhY3nu5PmuIUnLCB2YWx1ZTogJ+azleivieWHj+WFjee7k+a4hScgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfms5Xor4nlhajpop3nu5PmuIUnLCB2YWx1ZTogJ+azleivieWFqOminee7k+a4hScgfSwNCiAgICAgICAgICBdLA0KICAgICAgICB9LA0KICAgICAgICB7IGxhYmVsOiAn5pKk5qGIJywgdmFsdWU6ICfmkqTmoYgnIH0sDQogICAgICBdLA0KICAgICAgLy8g5b2T5YmN5r+A5rS755qE5q2l6aqk57Si5byVDQogICAgICBhY3RpdmVTdGVwOiAwLA0KICAgICAgbG9nTGlzdDogW10sDQogICAgICAvLyDmlrDlop7nmoTmlbDmja7lsZ7mgKcNCiAgICAgIHNlYXJjaEtleXdvcmQ6ICcnLA0KICAgICAgZmlsdGVyU3RhdHVzOiAnJywNCiAgICAgIGRhdGVSYW5nZTogW10sDQogICAgICB2aWV3TW9kZTogJ3RhYmxlJywgLy8gdGFibGUg5oiWIHRpbWVsaW5lDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOS7jueKtuaAgeagkeS4reaPkOWPlueItuiKgueCueeahGxhYmVs5L2c5Li66L+b5bqm5p2h5q2l6aqkDQogICAgc3RhdHVzU3RlcHMoKSB7DQogICAgICByZXR1cm4gdGhpcy5saXRpZ2F0aW9uU3RhdHVzVHJlZS5tYXAoaXRlbSA9PiBpdGVtLmxhYmVsKQ0KICAgIH0sDQoNCiAgICAvLyDnrZvpgInlkI7nmoTml6Xlv5fliJfooagNCiAgICBmaWx0ZXJlZExvZ0xpc3QoKSB7DQogICAgICBsZXQgZmlsdGVyZWQgPSBbLi4udGhpcy5sb2dMaXN0XQ0KDQogICAgICAvLyDlhbPplK7or43mkJzntKINCiAgICAgIGlmICh0aGlzLnNlYXJjaEtleXdvcmQpIHsNCiAgICAgICAgY29uc3Qga2V5d29yZCA9IHRoaXMuc2VhcmNoS2V5d29yZC50b0xvd2VyQ2FzZSgpDQogICAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKGxvZyA9Pg0KICAgICAgICAgIChsb2cuZG9jTmFtZSAmJiBsb2cuZG9jTmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGtleXdvcmQpKSB8fA0KICAgICAgICAgIChsb2cuY3JlYXRlQnkgJiYgbG9nLmNyZWF0ZUJ5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoa2V5d29yZCkpIHx8DQogICAgICAgICAgKGxvZy5zdGF0dXMgJiYgbG9nLnN0YXR1cy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGtleXdvcmQpKSB8fA0KICAgICAgICAgIChsb2cuZG9jTnVtYmVyICYmIGxvZy5kb2NOdW1iZXIudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSkNCiAgICAgICAgKQ0KICAgICAgfQ0KDQogICAgICAvLyDnirbmgIHnrZvpgIkNCiAgICAgIGlmICh0aGlzLmZpbHRlclN0YXR1cykgew0KICAgICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihsb2cgPT4gbG9nLnN0YXR1cyA9PT0gdGhpcy5maWx0ZXJTdGF0dXMpDQogICAgICB9DQoNCiAgICAgIC8vIOaXpeacn+iMg+WbtOetm+mAiQ0KICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSh0aGlzLmRhdGVSYW5nZVswXSkNCiAgICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHRoaXMuZGF0ZVJhbmdlWzFdKQ0KICAgICAgICBlbmREYXRlLnNldEhvdXJzKDIzLCA1OSwgNTksIDk5OSkgLy8g6K6+572u5Li65b2T5aSp57uT5p2f5pe26Ze0DQoNCiAgICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIobG9nID0+IHsNCiAgICAgICAgICBpZiAoIWxvZy5jcmVhdGVUaW1lKSByZXR1cm4gZmFsc2UNCiAgICAgICAgICBjb25zdCBsb2dEYXRlID0gbmV3IERhdGUobG9nLmNyZWF0ZVRpbWUpDQogICAgICAgICAgcmV0dXJuIGxvZ0RhdGUgPj0gc3RhcnREYXRlICYmIGxvZ0RhdGUgPD0gZW5kRGF0ZQ0KICAgICAgICB9KQ0KICAgICAgfQ0KDQogICAgICAvLyDmjInml7bpl7TlgJLluo/mjpLliJcNCiAgICAgIHJldHVybiBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiLmNyZWF0ZVRpbWUpIC0gbmV3IERhdGUoYS5jcmVhdGVUaW1lKSkNCiAgICB9LA0KDQogICAgLy8g5omA5pyJ54q25oCB5YiX6KGo77yI55So5LqO562b6YCJ5LiL5ouJ5qGG77yJDQogICAgYWxsU3RhdHVzZXMoKSB7DQogICAgICBjb25zdCBzdGF0dXNlcyA9IG5ldyBTZXQoKQ0KICAgICAgdGhpcy5sb2dMaXN0LmZvckVhY2gobG9nID0+IHsNCiAgICAgICAgaWYgKGxvZy5zdGF0dXMpIHsNCiAgICAgICAgICBzdGF0dXNlcy5hZGQobG9nLnN0YXR1cykNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHJldHVybiBBcnJheS5mcm9tKHN0YXR1c2VzKS5zb3J0KCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliqDovb3ml6Xlv5fmlbDmja4NCiAgICBhc3luYyBsb2FkTG9nRGF0YSgpIHsNCiAgICAgIGlmICghdGhpcy5kYXRhIHx8ICF0aGlzLmRhdGEu5bqP5Y+3KSByZXR1cm4NCg0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgbGlzdExpdGlnYXRpb25fbG9nKHsgbGl0aWdhdGlvbklkOiB0aGlzLmRhdGEu5bqP5Y+3IH0pDQogICAgICAgIHRoaXMubG9nTGlzdCA9IHJlcy5yb3dzIHx8IFtdDQoNCiAgICAgICAgLy8g6I635Y+W5pyA5ZCO5LiA5Liq5pel5b+X55qE54q25oCB77yM5bm26K6+572u5a+55bqU55qE6L+b5bqm5p2h5q2l6aqkDQogICAgICAgIGlmICh0aGlzLmxvZ0xpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnN0IGxhc3RMb2dTdGF0dXMgPSB0aGlzLmxvZ0xpc3RbdGhpcy5sb2dMaXN0Lmxlbmd0aCAtIDFdLnN0YXR1cw0KICAgICAgICAgIHRoaXMuc2V0QWN0aXZlU3RlcEJ5U3RhdHVzKGxhc3RMb2dTdGF0dXMpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaXpeW/l+aVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L295pel5b+X5pWw5o2u5aSx6LSlJykNCiAgICAgICAgdGhpcy5sb2dMaXN0ID0gW10NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOagueaNrueKtuaAgeiuvue9rua/gOa0u+eahOatpemqpA0KICAgIHNldEFjdGl2ZVN0ZXBCeVN0YXR1cyhzdGF0dXMpIHsNCiAgICAgIGlmICghc3RhdHVzKSB7DQogICAgICAgIHRoaXMuYWN0aXZlU3RlcCA9IDANCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOafpeaJvueKtuaAgeWvueW6lOeahOeItuiKgueCuee0ouW8lQ0KICAgICAgY29uc3QgcGFyZW50SW5kZXggPSB0aGlzLmZpbmRQYXJlbnRJbmRleEJ5U3RhdHVzKHN0YXR1cykNCiAgICAgIHRoaXMuYWN0aXZlU3RlcCA9IHBhcmVudEluZGV4ID49IDAgPyBwYXJlbnRJbmRleCA6IDANCiAgICB9LA0KDQogICAgLy8g5qC55o2u54q25oCB5om+5Yiw54i26IqC54K555qE57Si5byVDQogICAgZmluZFBhcmVudEluZGV4QnlTdGF0dXMoc3RhdHVzKSB7DQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubGl0aWdhdGlvblN0YXR1c1RyZWUubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMubGl0aWdhdGlvblN0YXR1c1RyZWVbaV0NCg0KICAgICAgICAvLyDlpoLmnpzmmK/niLboioLngrnmnKzouqsNCiAgICAgICAgaWYgKGl0ZW0ubGFiZWwgPT09IHN0YXR1cyB8fCBpdGVtLnZhbHVlID09PSBzdGF0dXMpIHsNCiAgICAgICAgICByZXR1cm4gaQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aaC5p6c5pyJ5a2Q6IqC54K577yM5Zyo5a2Q6IqC54K55Lit5p+l5om+DQogICAgICAgIGlmIChpdGVtLmNoaWxkcmVuICYmIGl0ZW0uY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnN0IGNoaWxkRm91bmQgPSBpdGVtLmNoaWxkcmVuLnNvbWUoY2hpbGQgPT4gY2hpbGQubGFiZWwgPT09IHN0YXR1cyB8fCBjaGlsZC52YWx1ZSA9PT0gc3RhdHVzKQ0KICAgICAgICAgIGlmIChjaGlsZEZvdW5kKSB7DQogICAgICAgICAgICByZXR1cm4gaQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIC0xDQogICAgfSwNCg0KICAgIC8vIOaQnOe0ouWkhOeQhg0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCiAgICAgIC8vIOaQnOe0oumAu+i+keWcqGNvbXB1dGVk5Lit5aSE55CG77yM6L+Z6YeM5Y+v5Lul5re75Yqg6Ziy5oqW562J5LyY5YyWDQogICAgfSwNCg0KICAgIC8vIOeKtuaAgeetm+mAieWkhOeQhg0KICAgIGhhbmRsZUZpbHRlcigpIHsNCiAgICAgIC8vIOetm+mAiemAu+i+keWcqGNvbXB1dGVk5Lit5aSE55CGDQogICAgfSwNCg0KICAgIC8vIOaXpeacn+etm+mAieWkhOeQhg0KICAgIGhhbmRsZURhdGVGaWx0ZXIoKSB7DQogICAgICAvLyDml6XmnJ/nrZvpgInpgLvovpHlnKhjb21wdXRlZOS4reWkhOeQhg0KICAgIH0sDQoNCiAgICAvLyDliLfmlrDmlbDmja4NCiAgICByZWZyZXNoRGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZExvZ0RhdGEoKQ0KICAgIH0sDQoNCiAgICAvLyDop4blm77mqKHlvI/liIfmjaINCiAgICBoYW5kbGVWaWV3TW9kZUNoYW5nZSgpIHsNCiAgICAgIC8vIOinhuWbvuWIh+aNoumAu+i+kQ0KICAgIH0sDQoNCiAgICAvLyDmn6XnnIvml6Xlv5for6bmg4UNCiAgICB2aWV3TG9nRGV0YWlsKGxvZykgew0KICAgICAgaWYgKGxvZy5kb2NVcGxvYWRVcmwpIHsNCiAgICAgICAgLy8g5omT5byA5paH5qGj6ZO+5o6lDQogICAgICAgIHdpbmRvdy5vcGVuKGxvZy5kb2NVcGxvYWRVcmwsICdfYmxhbmsnKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmmoLml6DmlofmoaPpk77mjqUnKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlr7zlh7rml6Xlv5cNCiAgICBleHBvcnRMb2dzKCkgew0KICAgICAgaWYgKHRoaXMuZmlsdGVyZWRMb2dMaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aaguaXoOaVsOaNruWPr+WvvOWHuicpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDov5nph4zlj6/ku6Xlrp7njrDlr7zlh7rlip/og70NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a+85Ye65Yqf6IO95byA5Y+R5LitLi4uJykNCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5pel5pyfDQogICAgZm9ybWF0RGF0ZShkYXRlKSB7DQogICAgICBpZiAoIWRhdGUpIHJldHVybiAnLScNCiAgICAgIHJldHVybiBuZXcgRGF0ZShkYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLUNOJykNCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5pel5pyf5pe26Ze0DQogICAgZm9ybWF0RGF0ZVRpbWUoZGF0ZXRpbWUpIHsNCiAgICAgIGlmICghZGF0ZXRpbWUpIHJldHVybiAnLScNCiAgICAgIHJldHVybiBuZXcgRGF0ZShkYXRldGltZSkudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJykNCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW6YeR6aKdDQogICAgZm9ybWF0TW9uZXkoYW1vdW50KSB7DQogICAgICBpZiAoYW1vdW50ID09PSBudWxsIHx8IGFtb3VudCA9PT0gdW5kZWZpbmVkIHx8IGFtb3VudCA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICcwLjAwJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIE51bWJlcihhbW91bnQpLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLA0KICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDINCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWCrOiusOexu+Wei+aWh+acrA0KICAgIGdldFVyZ2VTdGF0dXNUZXh0KHZhbHVlKSB7DQogICAgICBjb25zdCB1cmdlU3RhdHVzTWFwID0gew0KICAgICAgICAxOiAn57un57ut6Lef6LiqJywNCiAgICAgICAgMjogJ+e6puWumui/mOasvicsDQogICAgICAgIDM6ICfml6Dms5Xot5/ov5snDQogICAgICB9DQogICAgICByZXR1cm4gdXJnZVN0YXR1c01hcFt2YWx1ZV0gfHwgdmFsdWUgfHwgJy0nDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWCrOiusOexu+Wei+agt+W8j+exuw0KICAgIGdldFVyZ2VTdGF0dXNDbGFzcyh2YWx1ZSkgew0KICAgICAgY29uc3QgY2xhc3NNYXAgPSB7DQogICAgICAgIDE6ICdzdGF0dXMtdHJhY2tpbmcnLA0KICAgICAgICAyOiAnc3RhdHVzLWFwcG9pbnRtZW50JywNCiAgICAgICAgMzogJ3N0YXR1cy11bmFibGUnDQogICAgICB9DQogICAgICByZXR1cm4gY2xhc3NNYXBbdmFsdWVdIHx8ICcnDQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueKtuaAgeagh+etvuexu+Weiw0KICAgIGdldFN0YXR1c1RhZ1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAn5YeG5aSH6LWE5paZJzogJ2luZm8nLA0KICAgICAgICAn5b6F56uL5qGIJzogJ3dhcm5pbmcnLA0KICAgICAgICAn5b6F5Ye65rCR5Yid5Y+3JzogJ3dhcm5pbmcnLA0KICAgICAgICAn5b6F5byA5bqtJzogJ3ByaW1hcnknLA0KICAgICAgICAn5b6F5Ye65rOV6Zmi5paH5LmmJzogJ3ByaW1hcnknLA0KICAgICAgICAn5omn6KGM5LitJzogJ3N1Y2Nlc3MnLA0KICAgICAgICAn57uT5qGIJzogJ3N1Y2Nlc3MnLA0KICAgICAgICAn5pKk5qGIJzogJ2RhbmdlcicNCiAgICAgIH0NCiAgICAgIHJldHVybiB0eXBlTWFwW3N0YXR1c10gfHwgJ2luZm8nDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluaXtumXtOe6v+exu+Weiw0KICAgIGdldFRpbWVsaW5lVHlwZShzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7DQogICAgICAgICfnu5PmoYgnOiAnc3VjY2VzcycsDQogICAgICAgICfmkqTmoYgnOiAnZGFuZ2VyJywNCiAgICAgICAgJ+aJp+ihjOS4rSc6ICdwcmltYXJ5Jw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHR5cGVNYXBbc3RhdHVzXSB8fCAncHJpbWFyeScNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5pe26Ze057q/6aKc6ImyDQogICAgZ2V0VGltZWxpbmVDb2xvcihzdGF0dXMpIHsNCiAgICAgIGNvbnN0IGNvbG9yTWFwID0gew0KICAgICAgICAn5YeG5aSH6LWE5paZJzogJyM5MDkzOTknLA0KICAgICAgICAn5b6F56uL5qGIJzogJyNFNkEyM0MnLA0KICAgICAgICAn5b6F5Ye65rCR5Yid5Y+3JzogJyNFNkEyM0MnLA0KICAgICAgICAn5b6F5byA5bqtJzogJyM0MDlFRkYnLA0KICAgICAgICAn5b6F5Ye65rOV6Zmi5paH5LmmJzogJyM0MDlFRkYnLA0KICAgICAgICAn5omn6KGM5LitJzogJyM2N0MyM0EnLA0KICAgICAgICAn57uT5qGIJzogJyM2N0MyM0EnLA0KICAgICAgICAn5pKk5qGIJzogJyNGNTZDNkMnDQogICAgICB9DQogICAgICByZXR1cm4gY29sb3JNYXBbc3RhdHVzXSB8fCAnIzQwOUVGRicNCiAgICB9LA0KDQogICAgb3BlbkRpYWxvZygpIHsNCiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMubG9hZExvZ0RhdGEoKQ0KICAgIH0sDQoNCiAgICBoYW5kbGVVcmdlTG9nKCkgew0KICAgICAgLy8g5omT5byA5YKs6K6w5pel5b+X5a+56K+d5qGG77yM5Lyg5YWl5b2T5YmN55qEIGRhdGENCiAgICAgIHRoaXMucmVtaW5kZXJMb2dMb2FuSWQgPSBTdHJpbmcodGhpcy5kYXRhLua1geeoi+W6j+WPtykNCiAgICAgIHRoaXMuJHJlZnMubG9hblJlbWluZGVyTG9nLm9wZW5Mb2dEaWFsb2coKQ0KICAgIH0sDQoNCiAgICBoYW5kbGVDb25maXJtKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2UNCiAgICB9LA0KDQogICAgaGFuZGxlQ2FuY2VsKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2UNCiAgICAgIC8vIOmHjee9ruetm+mAieadoeS7tg0KICAgICAgdGhpcy5zZWFyY2hLZXl3b3JkID0gJycNCiAgICAgIHRoaXMuZmlsdGVyU3RhdHVzID0gJycNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW10NCiAgICAgIHRoaXMudmlld01vZGUgPSAndGFibGUnDQogICAgfSwNCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["litigationLogView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationLogView.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"法诉日志查看\" width=\"1200px\" @close=\"handleCancel\" class=\"litigation-log-dialog\">\r\n      <!-- 案件基本信息 -->\r\n      <div class=\"case-info-card\" v-if=\"data\">\r\n        <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">案件基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">贷款人：</span>\r\n                <span class=\"value\">{{ data.贷款人 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">身份证：</span>\r\n                <span class=\"value\">{{ data.身份证 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">车牌号：</span>\r\n                <span class=\"value\">{{ data.车辆牌号 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">法诉文员：</span>\r\n                <span class=\"value\">{{ data.法诉文员 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 10px;\">\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">发起法诉日：</span>\r\n                <span class=\"value\">{{ formatDate(data.发起法诉日) || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">法诉状态：</span>\r\n                <span class=\"value\">{{ data.法诉子状态 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">剩余金额：</span>\r\n                <span class=\"value amount\">{{ formatMoney(data.剩余金额) || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">最新催记类型：</span>\r\n                <span class=\"value\" :class=\"getUrgeStatusClass(data.日志类型)\">\r\n                  {{ getUrgeStatusText(data.日志类型) || '-' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </div>\r\n\r\n      <!-- 进度条 -->\r\n      <el-steps :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 筛选和搜索 -->\r\n      <div class=\"filter-section\" style=\"margin-bottom: 20px;\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-input\r\n              v-model=\"searchKeyword\"\r\n              placeholder=\"搜索日志内容、操作人\"\r\n              prefix-icon=\"el-icon-search\"\r\n              clearable\r\n              @input=\"handleSearch\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-select v-model=\"filterStatus\" placeholder=\"筛选状态\" clearable @change=\"handleFilter\">\r\n              <el-option label=\"全部\" value=\"\" />\r\n              <el-option v-for=\"status in allStatuses\" :key=\"status\" :label=\"status\" :value=\"status\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              @change=\"handleDateFilter\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"refreshData\">刷新</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 视图切换 -->\r\n      <div class=\"view-toggle\" style=\"margin-bottom: 20px;\">\r\n        <el-radio-group v-model=\"viewMode\" @change=\"handleViewModeChange\">\r\n          <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          <el-radio-button label=\"timeline\">时间线视图</el-radio-button>\r\n        </el-radio-group>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\" v-loading=\"loading\">\r\n        <el-table :data=\"filteredLogList\" border style=\"width: 100%; margin-bottom: 24px\" :height=\"400\">\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n          <el-table-column prop=\"createTime\" label=\"操作时间\" width=\"160\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.createTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"操作人\" width=\"120\" align=\"center\" />\r\n          <el-table-column prop=\"status\" label=\"法诉状态\" width=\"140\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\r\n                {{ scope.row.status || '-' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"docName\" label=\"文档名称\" width=\"150\" align=\"center\" />\r\n          <el-table-column prop=\"docNumber\" label=\"文档编号\" width=\"120\" align=\"center\" />\r\n          <el-table-column prop=\"openDate\" label=\"生效日期\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDate(scope.row.openDate) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"100\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"mini\"\r\n                @click=\"viewLogDetail(scope.row)\"\r\n                v-if=\"scope.row.docUploadUrl\"\r\n              >\r\n                查看文档\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 时间线视图 -->\r\n      <div v-if=\"viewMode === 'timeline'\" v-loading=\"loading\" class=\"timeline-container\">\r\n        <el-timeline>\r\n          <el-timeline-item\r\n            v-for=\"(log, index) in filteredLogList\"\r\n            :key=\"index\"\r\n            :timestamp=\"formatDateTime(log.createTime)\"\r\n            placement=\"top\"\r\n            :type=\"getTimelineType(log.status)\"\r\n            :color=\"getTimelineColor(log.status)\"\r\n            size=\"large\"\r\n          >\r\n            <el-card shadow=\"hover\" class=\"timeline-card\">\r\n              <div class=\"timeline-header\">\r\n                <span class=\"timeline-title\">{{ log.status || '状态更新' }}</span>\r\n                <span class=\"timeline-operator\">{{ log.createBy }}</span>\r\n              </div>\r\n              <div class=\"timeline-content\">\r\n                <p v-if=\"log.docName\"><strong>文档：</strong>{{ log.docName }}</p>\r\n                <p v-if=\"log.docNumber\"><strong>编号：</strong>{{ log.docNumber }}</p>\r\n                <p v-if=\"log.openDate\"><strong>生效日期：</strong>{{ formatDate(log.openDate) }}</p>\r\n                <div v-if=\"log.docUploadUrl\" class=\"timeline-actions\">\r\n                  <el-button type=\"text\" size=\"mini\" @click=\"viewLogDetail(log)\">查看文档</el-button>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"filteredLogList.length === 0\" class=\"empty-state\">\r\n          <el-empty description=\"暂无日志记录\" />\r\n        </div>\r\n      </div>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\" icon=\"el-icon-document\">催记日志</el-button>\r\n        <el-button @click=\"exportLogs\" icon=\"el-icon-download\">导出日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        if (newVal && newVal.序号) {\r\n          this.loadLogData()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      loading: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '撤案已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '执行终本', value: '执行终本' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n        { label: '撤案', value: '撤案' },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n      // 新增的数据属性\r\n      searchKeyword: '',\r\n      filterStatus: '',\r\n      dateRange: [],\r\n      viewMode: 'table', // table 或 timeline\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤\r\n    statusSteps() {\r\n      return this.litigationStatusTree.map(item => item.label)\r\n    },\r\n\r\n    // 筛选后的日志列表\r\n    filteredLogList() {\r\n      let filtered = [...this.logList]\r\n\r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase()\r\n        filtered = filtered.filter(log =>\r\n          (log.docName && log.docName.toLowerCase().includes(keyword)) ||\r\n          (log.createBy && log.createBy.toLowerCase().includes(keyword)) ||\r\n          (log.status && log.status.toLowerCase().includes(keyword)) ||\r\n          (log.docNumber && log.docNumber.toLowerCase().includes(keyword))\r\n        )\r\n      }\r\n\r\n      // 状态筛选\r\n      if (this.filterStatus) {\r\n        filtered = filtered.filter(log => log.status === this.filterStatus)\r\n      }\r\n\r\n      // 日期范围筛选\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        const startDate = new Date(this.dateRange[0])\r\n        const endDate = new Date(this.dateRange[1])\r\n        endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间\r\n\r\n        filtered = filtered.filter(log => {\r\n          if (!log.createTime) return false\r\n          const logDate = new Date(log.createTime)\r\n          return logDate >= startDate && logDate <= endDate\r\n        })\r\n      }\r\n\r\n      // 按时间倒序排列\r\n      return filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))\r\n    },\r\n\r\n    // 所有状态列表（用于筛选下拉框）\r\n    allStatuses() {\r\n      const statuses = new Set()\r\n      this.logList.forEach(log => {\r\n        if (log.status) {\r\n          statuses.add(log.status)\r\n        }\r\n      })\r\n      return Array.from(statuses).sort()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载日志数据\r\n    async loadLogData() {\r\n      if (!this.data || !this.data.序号) return\r\n\r\n      this.loading = true\r\n      try {\r\n        const res = await listLitigation_log({ litigationId: this.data.序号 })\r\n        this.logList = res.rows || []\r\n\r\n        // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n        if (this.logList.length > 0) {\r\n          const lastLogStatus = this.logList[this.logList.length - 1].status\r\n          this.setActiveStepByStatus(lastLogStatus)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载日志数据失败:', error)\r\n        this.$message.error('加载日志数据失败')\r\n        this.logList = []\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch() {\r\n      // 搜索逻辑在computed中处理，这里可以添加防抖等优化\r\n    },\r\n\r\n    // 状态筛选处理\r\n    handleFilter() {\r\n      // 筛选逻辑在computed中处理\r\n    },\r\n\r\n    // 日期筛选处理\r\n    handleDateFilter() {\r\n      // 日期筛选逻辑在computed中处理\r\n    },\r\n\r\n    // 刷新数据\r\n    refreshData() {\r\n      this.loadLogData()\r\n    },\r\n\r\n    // 视图模式切换\r\n    handleViewModeChange() {\r\n      // 视图切换逻辑\r\n    },\r\n\r\n    // 查看日志详情\r\n    viewLogDetail(log) {\r\n      if (log.docUploadUrl) {\r\n        // 打开文档链接\r\n        window.open(log.docUploadUrl, '_blank')\r\n      } else {\r\n        this.$message.info('暂无文档链接')\r\n      }\r\n    },\r\n\r\n    // 导出日志\r\n    exportLogs() {\r\n      if (this.filteredLogList.length === 0) {\r\n        this.$message.warning('暂无数据可导出')\r\n        return\r\n      }\r\n\r\n      // 这里可以实现导出功能\r\n      this.$message.success('导出功能开发中...')\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '-'\r\n      return new Date(date).toLocaleDateString('zh-CN')\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(datetime) {\r\n      if (!datetime) return '-'\r\n      return new Date(datetime).toLocaleString('zh-CN')\r\n    },\r\n\r\n    // 格式化金额\r\n    formatMoney(amount) {\r\n      if (amount === null || amount === undefined || amount === '') {\r\n        return '0.00'\r\n      }\r\n      return Number(amount).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n    },\r\n\r\n    // 获取催记类型文本\r\n    getUrgeStatusText(value) {\r\n      const urgeStatusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进'\r\n      }\r\n      return urgeStatusMap[value] || value || '-'\r\n    },\r\n\r\n    // 获取催记类型样式类\r\n    getUrgeStatusClass(value) {\r\n      const classMap = {\r\n        1: 'status-tracking',\r\n        2: 'status-appointment',\r\n        3: 'status-unable'\r\n      }\r\n      return classMap[value] || ''\r\n    },\r\n\r\n    // 获取状态标签类型\r\n    getStatusTagType(status) {\r\n      const typeMap = {\r\n        '准备资料': 'info',\r\n        '待立案': 'warning',\r\n        '待出民初号': 'warning',\r\n        '待开庭': 'primary',\r\n        '待出法院文书': 'primary',\r\n        '执行中': 'success',\r\n        '结案': 'success',\r\n        '撤案': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n\r\n    // 获取时间线类型\r\n    getTimelineType(status) {\r\n      const typeMap = {\r\n        '结案': 'success',\r\n        '撤案': 'danger',\r\n        '执行中': 'primary'\r\n      }\r\n      return typeMap[status] || 'primary'\r\n    },\r\n\r\n    // 获取时间线颜色\r\n    getTimelineColor(status) {\r\n      const colorMap = {\r\n        '准备资料': '#909399',\r\n        '待立案': '#E6A23C',\r\n        '待出民初号': '#E6A23C',\r\n        '待开庭': '#409EFF',\r\n        '待出法院文书': '#409EFF',\r\n        '执行中': '#67C23A',\r\n        '结案': '#67C23A',\r\n        '撤案': '#F56C6C'\r\n      }\r\n      return colorMap[status] || '#409EFF'\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n      this.loadLogData()\r\n    },\r\n\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n\r\n    handleConfirm() {\r\n      this.visible = false\r\n    },\r\n\r\n    handleCancel() {\r\n      this.visible = false\r\n      // 重置筛选条件\r\n      this.searchKeyword = ''\r\n      this.filterStatus = ''\r\n      this.dateRange = []\r\n      this.viewMode = 'table'\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 案件信息卡片 */\r\n.case-info-card .card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.case-info-card .card-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.case-info-card .info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.case-info-card .info-item .label {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  min-width: 80px;\r\n}\r\n\r\n.case-info-card .info-item .value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.case-info-card .info-item .value.amount {\r\n  color: #E6A23C;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 催记状态样式 */\r\n.status-tracking {\r\n  color: #409EFF;\r\n}\r\n\r\n.status-appointment {\r\n  color: #67C23A;\r\n}\r\n\r\n.status-unable {\r\n  color: #F56C6C;\r\n}\r\n\r\n/* 筛选区域 */\r\n.filter-section {\r\n  background: #f5f7fa;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 视图切换 */\r\n.view-toggle {\r\n  text-align: center;\r\n}\r\n\r\n/* 时间线容器 */\r\n.timeline-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n/* 时间线卡片 */\r\n.timeline-card {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.timeline-card .timeline-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.timeline-card .timeline-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.timeline-card .timeline-operator {\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.timeline-card .timeline-content p {\r\n  margin: 8px 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.timeline-card .timeline-content strong {\r\n  color: #303133;\r\n}\r\n\r\n.timeline-card .timeline-actions {\r\n  margin-top: 12px;\r\n  text-align: right;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 40px 0;\r\n}\r\n\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 时间线样式优化 */\r\n::v-deep .el-timeline-item__timestamp {\r\n  font-size: 13px;\r\n  color: #909399;\r\n}\r\n\r\n::v-deep .el-timeline-item__wrapper {\r\n  padding-left: 28px;\r\n}\r\n\r\n::v-deep .el-timeline-item__tail {\r\n  border-left: 2px solid #e4e7ed;\r\n}\r\n\r\n/* 表格样式优化 */\r\n::v-deep .el-table .el-table__header-wrapper {\r\n  background: #fafafa;\r\n}\r\n\r\n::v-deep .el-table .el-table__row:hover > td {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 对话框底部按钮 */\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  ::v-deep .litigation-log-dialog {\r\n    width: 95% !important;\r\n  }\r\n\r\n  .case-info-card .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .filter-section .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n"]}]}